-- MyCareerOfflineTargeted.lua - NBA 2K23 MyCareer Offline Mode (Targeted Patch)
-- Based on debugger findings - patches the exact NBA2K21_core pattern found

local lang = {}
lang.EN = {
    "MyCareer Offline (Targeted)",
    "Removes MyCareer online requirement using the exact pattern found by debugger"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V1.1",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Targeted patch based on debugger findings
local targeted_patch = {
    -- The exact pattern found by the debugger
    pattern = "3B CE 74 35",
    found_address = 0x7FF6734B9607,  -- Address from debugger log
    patch_bytes = "90 90 90 90",     -- 4 NOPs to disable the check
    original_bytes = nil,
    applied = false,
    verified = false
}

-- Plugin state
local mycareer = {
    enabled = false,
    initialized = false
}

-- Verify the pattern is still at the expected address
function mycareer.verify_pattern()
    print("[MyCareer-T] Verifying pattern at address: " .. string.format("0x%X", targeted_patch.found_address))
    
    local ptr = Pointer(targeted_patch.found_address)
    if not ptr then
        print("[MyCareer-T] ERROR: Could not create pointer for address")
        return false
    end
    
    local success, current_bytes = pcall(function()
        return ptr:readByteArrayString(4)
    end)
    
    if not success then
        print("[MyCareer-T] ERROR: Could not read bytes from address")
        return false
    end
    
    print("[MyCareer-T] Current bytes at address: " .. current_bytes)
    print("[MyCareer-T] Expected pattern: " .. targeted_patch.pattern)
    
    -- Check if it matches our expected pattern or if it's already patched
    if current_bytes == targeted_patch.pattern then
        print("[MyCareer-T] ✓ Pattern verified - ready to patch")
        targeted_patch.original_bytes = current_bytes
        targeted_patch.verified = true
        return true
    elseif current_bytes == targeted_patch.patch_bytes then
        print("[MyCareer-T] ✓ Pattern already patched")
        targeted_patch.original_bytes = targeted_patch.pattern  -- Store what it should be
        targeted_patch.applied = true
        targeted_patch.verified = true
        return true
    else
        print("[MyCareer-T] ⚠ WARNING: Pattern mismatch!")
        print("[MyCareer-T] This might mean the address has changed or the game was updated")
        return false
    end
end

-- Apply the targeted patch
function mycareer.apply_patch()
    print("[MyCareer-T] Applying targeted patch...")
    
    if not mycareer.verify_pattern() then
        print("[MyCareer-T] Pattern verification failed - aborting patch")
        return false
    end
    
    if targeted_patch.applied then
        print("[MyCareer-T] Patch already applied")
        return true
    end
    
    local ptr = Pointer(targeted_patch.found_address)
    if not ptr then
        print("[MyCareer-T] ERROR: Could not create pointer")
        return false
    end
    
    print("[MyCareer-T] Patching address " .. string.format("0x%X", targeted_patch.found_address))
    print("[MyCareer-T] Original: " .. targeted_patch.original_bytes)
    print("[MyCareer-T] Patch:    " .. targeted_patch.patch_bytes)
    
    local success, err = pcall(function()
        ptr:writeByteArrayString(targeted_patch.patch_bytes)
    end)
    
    if success then
        print("[MyCareer-T] ✓ SUCCESS: Patch applied!")
        targeted_patch.applied = true
        mycareer.enabled = true
        
        if Hook.showNotify then
            Hook.showNotify("MyCareer offline patch applied! Try accessing MyCareer now.", 1)
        end
        return true
    else
        print("[MyCareer-T] ✗ ERROR: Failed to apply patch: " .. tostring(err))
        return false
    end
end

-- Remove the patch (restore original bytes)
function mycareer.remove_patch()
    print("[MyCareer-T] Removing patch...")
    
    if not targeted_patch.applied then
        print("[MyCareer-T] No patch to remove")
        return true
    end
    
    if not targeted_patch.original_bytes then
        print("[MyCareer-T] ERROR: No original bytes stored")
        return false
    end
    
    local ptr = Pointer(targeted_patch.found_address)
    if not ptr then
        print("[MyCareer-T] ERROR: Could not create pointer")
        return false
    end
    
    print("[MyCareer-T] Restoring original bytes: " .. targeted_patch.original_bytes)
    
    local success, err = pcall(function()
        ptr:writeByteArrayString(targeted_patch.original_bytes)
    end)
    
    if success then
        print("[MyCareer-T] ✓ SUCCESS: Original bytes restored")
        targeted_patch.applied = false
        mycareer.enabled = false
        
        if Hook.showNotify then
            Hook.showNotify("MyCareer online requirement restored", 2)
        end
        return true
    else
        print("[MyCareer-T] ✗ ERROR: Failed to restore bytes: " .. tostring(err))
        return false
    end
end

-- Toggle the patch
function mycareer.toggle()
    if mycareer.enabled then
        mycareer.remove_patch()
    else
        mycareer.apply_patch()
    end
end

-- Plugin lifecycle
function Init()
    print("[MyCareer-T] MyCareer Offline (Targeted) initializing...")
    
    if not mycareer.initialized then
        print("[MyCareer-T] Based on debugger findings:")
        print("[MyCareer-T] Target address: " .. string.format("0x%X", targeted_patch.found_address))
        print("[MyCareer-T] Target pattern: " .. targeted_patch.pattern)
        
        -- Verify the pattern exists
        if mycareer.verify_pattern() then
            print("[MyCareer-T] Pattern verified - ready to patch")
            
            -- Auto-apply the patch
            if mycareer.apply_patch() then
                print("[MyCareer-T] ✓ Auto-patch successful!")
            else
                print("[MyCareer-T] ✗ Auto-patch failed")
            end
        else
            print("[MyCareer-T] ⚠ Pattern verification failed")
            if Hook.showNotify then
                Hook.showNotify("MyCareer pattern verification failed - address may have changed", 3)
            end
        end
        
        mycareer.initialized = true
    end
    
    print("[MyCareer-T] Plugin initialized")
    return true
end

function DeInit()
    print("[MyCareer-T] Deinitializing...")
    mycareer.remove_patch()
    mycareer.initialized = false
    return true
end

-- GUI
function OnDrawMenuItem()
    if not mycareer.initialized then
        return
    end
    
    if ImGui then
        if ImGui.BeginMenu("MyCareer Offline (Targeted)") then
            
            -- Status display
            local status_text = mycareer.enabled and "Status: ENABLED (Patched)" or "Status: DISABLED"
            ImGui.Text(status_text)
            
            local verify_text = targeted_patch.verified and "Pattern: Verified" or "Pattern: Not Verified"
            ImGui.Text(verify_text)
            
            ImGui.Text("Target: " .. string.format("0x%X", targeted_patch.found_address))
            
            ImGui.Separator()
            
            -- Toggle button
            local button_text = mycareer.enabled and "Disable (Restore Online Requirement)" or "Enable (Remove Online Requirement)"
            if ImGui.MenuItem(button_text) then
                mycareer.toggle()
            end
            
            ImGui.Separator()
            
            -- Manual controls
            if ImGui.MenuItem("Re-verify Pattern") then
                if mycareer.verify_pattern() then
                    if Hook.showNotify then
                        Hook.showNotify("Pattern re-verified successfully", 1)
                    end
                else
                    if Hook.showNotify then
                        Hook.showNotify("Pattern verification failed", 3)
                    end
                end
            end
            
            if ImGui.MenuItem("Force Apply Patch") then
                if mycareer.apply_patch() then
                    if Hook.showNotify then
                        Hook.showNotify("Patch force-applied", 1)
                    end
                end
            end
            
            if ImGui.MenuItem("Force Remove Patch") then
                if mycareer.remove_patch() then
                    if Hook.showNotify then
                        Hook.showNotify("Patch force-removed", 2)
                    end
                end
            end
            
            ImGui.Separator()
            
            -- Info
            ImGui.Text("Version: V1.1 (Targeted)")
            ImGui.Text("Based on debugger findings")
            
            if mycareer.enabled then
                ImGui.TextColored({0.0, 1.0, 0.0, 1.0}, "MyCareer should work offline!")
            else
                ImGui.TextColored({1.0, 0.5, 0.0, 1.0}, "Online requirement active")
            end
            
            ImGui.EndMenu()
        end
    end
end

-- Console commands
function mct_toggle()
    mycareer.toggle()
end

function mct_status()
    local status = mycareer.enabled and "ENABLED" or "DISABLED"
    local verified = targeted_patch.verified and "VERIFIED" or "NOT VERIFIED"
    print("[MyCareer-T] Status: " .. status .. ", Pattern: " .. verified)
    print("[MyCareer-T] Address: " .. string.format("0x%X", targeted_patch.found_address))
end

function mct_verify()
    mycareer.verify_pattern()
end

function mct_help()
    print("[MyCareer-T] Console Commands:")
    print("  mct_toggle() - Toggle patch on/off")
    print("  mct_status() - Show status")
    print("  mct_verify() - Re-verify pattern")
    print("  mct_help()   - Show this help")
end

print("[MyCareer-T] Targeted MyCareer Offline Plugin loaded!")
print("[MyCareer-T] Target: " .. string.format("0x%X", targeted_patch.found_address))
print("[MyCareer-T] Console: mct_toggle(), mct_status(), mct_verify(), mct_help()")
print("[MyCareer-T] GUI: F8 -> MyCareer Offline (Targeted)")

-- Show initial status
mct_status()
