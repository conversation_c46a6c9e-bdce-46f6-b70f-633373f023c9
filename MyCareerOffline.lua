-- MyCareerOffline.lua - NBA 2K23 MyCareer Offline Mode Plugin
-- Removes the online requirement for MyCareer mode based on NBA 2K21 cheat table

local lang = {}
lang.EN = {
    "MyCareer Offline Mode",
    "Removes the online requirement for MyCareer mode. Play MyCareer without internet connection!"
}
lang.ZH = {
    "MyCareer离线模式",
    "移除MyCareer模式的在线要求。无需网络连接即可游玩MyCareer！"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V1.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Memory patches based on NBA 2K21 working cheat table
local patches = {
    -- Online requirement check - based on NBA 2K21 pattern
    online_check = {
        -- NBA 2K21 pattern: 85 C9 0F 84 * * * * 3B CE 74 35 41 B8
        -- We'll search for similar patterns in NBA 2K23
        patterns = {
            "85 C9 0F 84 ? ? ? ? 3B CE 74 35 41 B8",  -- Exact NBA 2K21 pattern
            "85 C9 0F 84 ? ? ? ? 3B CE 74 35",        -- Shorter version
            "3B CE 74 35 41 B8",                      -- Just the patch area
            "85 C9 0F 84 ? ? ? ? 3B",                 -- Even shorter
            "3B CE 74 ?",                             -- Very short pattern
        },
        patch_offset = 8,  -- Offset to the "3B CE 74 35" part
        original_bytes = "3B CE 74 35",  -- Original bytes to replace
        patch_bytes = "90 90 90 90",     -- NOPs to disable the check
        address = nil,
        original = nil,
        applied = false
    }
}

-- Plugin state
local mycareer_offline = {
    enabled = true,
    initialized = false,
    patches_applied = false,
    gui_enabled = true,
    show_status = true
}

-- Try to find and patch the online requirement check
function mycareer_offline.apply_online_patches()
    print("[MyCareer] Searching for online requirement check patterns...")

    if not Hook or not Hook.markCodeScan then
        print("[MyCareer] Hook.markCodeScan not available")
        return false
    end

    local patch_info = patches.online_check

    -- Try each pattern until we find one
    for i, pattern in ipairs(patch_info.patterns) do
        print("[MyCareer] Trying pattern " .. i .. ": " .. pattern)
        local addr = Hook.markCodeScan(pattern)
        
        if addr and addr ~= 0 then
            print("[MyCareer] Found online check pattern at: " .. string.format("0x%X", addr))
            
            -- Calculate the actual patch address
            local patch_addr = addr + patch_info.patch_offset
            print("[MyCareer] Patch address: " .. string.format("0x%X", patch_addr))
            
            local ptr = Pointer(patch_addr)
            if ptr then
                -- Read original bytes first
                local success, original = pcall(function()
                    return ptr:readByteArrayString(4)  -- Read 4 bytes
                end)
                
                if success then
                    patch_info.original = original
                    patch_info.address = patch_addr
                    
                    print("[MyCareer] Original bytes: " .. original)
                    print("[MyCareer] Applying patch: " .. patch_info.patch_bytes)
                    
                    -- Apply the patch
                    local patch_success, err = pcall(function()
                        ptr:writeByteArrayString(patch_info.patch_bytes)
                    end)
                    
                    if patch_success then
                        print("[MyCareer] Successfully patched online requirement check!")
                        patch_info.applied = true
                        mycareer_offline.patches_applied = true
                        
                        if Hook.showNotify then
                            Hook.showNotify("MyCareer offline mode enabled! Online requirement removed.", 1)
                        end
                        return true
                    else
                        print("[MyCareer] Failed to apply patch: " .. tostring(err))
                    end
                else
                    print("[MyCareer] Failed to read original bytes")
                end
            else
                print("[MyCareer] Failed to create pointer for patch address")
            end
        else
            print("[MyCareer] Pattern " .. i .. " not found")
        end
    end

    print("[MyCareer] No suitable online check patterns found")
    return false
end

-- Try alternative patterns that might exist in NBA 2K23
function mycareer_offline.try_alternative_patterns()
    print("[MyCareer] Trying alternative online check patterns...")

    if not Hook or not Hook.markCodeScan then
        return false
    end

    -- Alternative patterns that might indicate online checks
    local alt_patterns = {
        -- Network/connection related patterns
        {pattern = "E8 ? ? ? ? 85 C0 74 ? 48 8B", name = "call_test_je_mov", patch_offset = 5, patch_bytes = "31 C0 90 90"},  -- xor eax,eax + nops
        {pattern = "E8 ? ? ? ? 85 C0 75 ?", name = "call_test_jne", patch_offset = 5, patch_bytes = "31 C0 90 90"},
        {pattern = "85 C0 0F 84 ? ? ? ?", name = "test_je_long", patch_offset = 0, patch_bytes = "31 C0 90 90 90 90 90 90"},  -- xor eax,eax + nops
        {pattern = "85 C0 74 ?", name = "test_je_short", patch_offset = 0, patch_bytes = "31 C0 90 90"},
        {pattern = "83 F8 00 74 ?", name = "cmp_0_je", patch_offset = 0, patch_bytes = "90 90 90 90 90"},  -- all nops
        {pattern = "83 F8 01 74 ?", name = "cmp_1_je", patch_offset = 0, patch_bytes = "90 90 90 90 90"},
        -- More specific MyCareer patterns
        {pattern = "74 ? 48 8D ? ? ? ? ? E8", name = "mycareer_check1", patch_offset = 0, patch_bytes = "EB"},  -- change je to jmp
        {pattern = "75 ? 48 8D ? ? ? ? ? E8", name = "mycareer_check2", patch_offset = 0, patch_bytes = "90 90"},  -- nop the jne
    }

    local patched_count = 0
    for i, pattern_info in ipairs(alt_patterns) do
        local addr = Hook.markCodeScan(pattern_info.pattern)
        if addr and addr ~= 0 then
            print("[MyCareer] Found " .. pattern_info.name .. " at: " .. string.format("0x%X", addr))

            -- Try to patch this one
            local patch_addr = addr + pattern_info.patch_offset
            local ptr = Pointer(patch_addr)
            if ptr then
                local success, err = pcall(function()
                    -- Read original bytes first
                    local original_len = string.len(pattern_info.patch_bytes:gsub(" ", "")) / 2
                    local original = ptr:readByteArrayString(original_len)

                    -- Apply patch
                    ptr:writeByteArrayString(pattern_info.patch_bytes)

                    print("[MyCareer] Patched " .. pattern_info.name .. " - original: " .. original)
                    patched_count = patched_count + 1
                end)

                if not success then
                    print("[MyCareer] Failed to patch " .. pattern_info.name .. ": " .. tostring(err))
                end
            end
        end
    end

    if patched_count > 0 then
        print("[MyCareer] Applied " .. patched_count .. " alternative patches")
        if Hook.showNotify then
            Hook.showNotify("Applied " .. patched_count .. " online check patches - test MyCareer now!", 1)
        end
        mycareer_offline.patches_applied = true
        return true
    end

    return false
end

-- Try to find MyCareer-specific strings and patch nearby code
function mycareer_offline.try_string_based_search()
    print("[MyCareer] Trying string-based search for MyCareer online checks...")

    if not Hook or not Hook.markCodeScan then
        return false
    end

    -- Look for patterns around common online error messages or checks
    local string_patterns = {
        -- Look for patterns that might be near "online" or "connection" checks
        "48 8D ? ? ? ? ? E8 ? ? ? ? 85 C0 74",  -- lea + call + test + je (common error check)
        "48 8D ? ? ? ? ? E8 ? ? ? ? 85 C0 75",  -- lea + call + test + jne
        "E8 ? ? ? ? 84 C0 74",                  -- call + test al + je
        "E8 ? ? ? ? 84 C0 75",                  -- call + test al + jne
    }

    local found_count = 0
    for i, pattern in ipairs(string_patterns) do
        local addr = Hook.markCodeScan(pattern)
        if addr and addr ~= 0 then
            print("[MyCareer] Found string-related pattern " .. i .. " at: " .. string.format("0x%X", addr))
            found_count = found_count + 1

            -- Try to patch the conditional jump
            local patch_attempts = {
                {offset = 13, bytes = "90 90"},  -- NOP the je/jne
                {offset = 12, bytes = "31 C0"},  -- xor eax,eax before the test
            }

            for _, attempt in ipairs(patch_attempts) do
                local patch_addr = addr + attempt.offset
                local ptr = Pointer(patch_addr)
                if ptr then
                    local success, err = pcall(function()
                        ptr:writeByteArrayString(attempt.bytes)
                    end)

                    if success then
                        print("[MyCareer] Applied string-based patch at offset " .. attempt.offset)
                        mycareer_offline.patches_applied = true
                    end
                end
            end
        end
    end

    return found_count > 0
end

-- Brute force approach - patch many conditional jumps that might be online checks
function mycareer_offline.try_brute_force_patches()
    print("[MyCareer] Trying brute force approach - patching common conditional jumps...")

    if not Hook or not Hook.markCodeScan then
        return false
    end

    -- Common conditional jump patterns that might be online checks
    local jump_patterns = {
        -- Short conditional jumps (2 bytes)
        {pattern = "74 ?", name = "je_short", patch = "EB"},      -- je -> jmp (always jump)
        {pattern = "75 ?", name = "jne_short", patch = "90 90"},  -- jne -> nop nop
        {pattern = "76 ?", name = "jbe_short", patch = "EB"},     -- jbe -> jmp
        {pattern = "77 ?", name = "ja_short", patch = "90 90"},   -- ja -> nop nop
        {pattern = "78 ?", name = "js_short", patch = "90 90"},   -- js -> nop nop
        {pattern = "79 ?", name = "jns_short", patch = "EB"},     -- jns -> jmp
        {pattern = "7C ?", name = "jl_short", patch = "90 90"},   -- jl -> nop nop
        {pattern = "7D ?", name = "jge_short", patch = "EB"},     -- jge -> jmp
        {pattern = "7E ?", name = "jle_short", patch = "90 90"},  -- jle -> nop nop
        {pattern = "7F ?", name = "jg_short", patch = "EB"},      -- jg -> jmp

        -- Test + conditional jump combinations
        {pattern = "85 C0 74 ?", name = "test_eax_je", patch = "85 C0 EB"},     -- test eax,eax; je -> jmp
        {pattern = "85 C0 75 ?", name = "test_eax_jne", patch = "85 C0 90 90"}, -- test eax,eax; jne -> nops
        {pattern = "84 C0 74 ?", name = "test_al_je", patch = "84 C0 EB"},      -- test al,al; je -> jmp
        {pattern = "84 C0 75 ?", name = "test_al_jne", patch = "84 C0 90 90"},  -- test al,al; jne -> nops

        -- Compare + conditional jump combinations
        {pattern = "83 F8 00 74 ?", name = "cmp_eax_0_je", patch = "83 F8 00 EB"},     -- cmp eax,0; je -> jmp
        {pattern = "83 F8 00 75 ?", name = "cmp_eax_0_jne", patch = "83 F8 00 90 90"}, -- cmp eax,0; jne -> nops
        {pattern = "83 F8 01 74 ?", name = "cmp_eax_1_je", patch = "83 F8 01 EB"},     -- cmp eax,1; je -> jmp
        {pattern = "83 F8 01 75 ?", name = "cmp_eax_1_jne", patch = "83 F8 01 90 90"}, -- cmp eax,1; jne -> nops
    }

    local patched_count = 0
    local max_patches = 10  -- Limit to avoid breaking too much

    for i, pattern_info in ipairs(jump_patterns) do
        if patched_count >= max_patches then
            print("[MyCareer] Reached maximum patch limit (" .. max_patches .. "), stopping")
            break
        end

        -- Find all instances of this pattern
        local search_addr = 0
        local instance_count = 0

        while instance_count < 3 do  -- Max 3 instances per pattern
            local addr = Hook.markCodeScan(pattern_info.pattern, search_addr)
            if not addr or addr == 0 then
                break
            end

            print("[MyCareer] Found " .. pattern_info.name .. " at: " .. string.format("0x%X", addr))

            local ptr = Pointer(addr)
            if ptr then
                local success, err = pcall(function()
                    -- Read original bytes for logging
                    local original_len = string.len(pattern_info.patch:gsub(" ", "")) / 2
                    local original = ptr:readByteArrayString(original_len)

                    -- Apply patch
                    ptr:writeByteArrayString(pattern_info.patch)

                    print("[MyCareer] Patched " .. pattern_info.name .. " - original: " .. original .. " -> new: " .. pattern_info.patch)
                    patched_count = patched_count + 1
                end)

                if not success then
                    print("[MyCareer] Failed to patch " .. pattern_info.name .. ": " .. tostring(err))
                end
            end

            search_addr = addr + 1  -- Search for next instance
            instance_count = instance_count + 1
        end
    end

    if patched_count > 0 then
        print("[MyCareer] Brute force applied " .. patched_count .. " patches")
        if Hook.showNotify then
            Hook.showNotify("Applied " .. patched_count .. " brute force patches - test MyCareer!", 1)
        end
        mycareer_offline.patches_applied = true
        return true
    end

    print("[MyCareer] Brute force found no patchable patterns")
    return false
end

-- Try patterns similar to the working NBA2K23 cheat table
function mycareer_offline.try_nba2k23_patterns()
    print("[MyCareer] Trying NBA 2K23-specific patterns from working cheat tables...")

    if not Hook or not Hook.markCodeScan then
        return false
    end

    -- Patterns from the working NBA2K23InfiniteRange.CT
    local nba2k23_patterns = {
        -- From the cheat table that works
        {pattern = "8B 41 0C FF C8 83 F8 01 77", name = "nba2k23_inject1",
         patch_offset = 5, patch_bytes = "90 90 90 90"},  -- NOP the cmp and ja
        {pattern = "89 B0 3C 02 00 00 48", name = "nba2k23_inject2",
         patch_offset = 0, patch_bytes = "90 90 90 90 90 90 90"},  -- NOP the entire mov

        -- Variations that might be MyCareer-specific
        {pattern = "8B 41 0C FF C8 83 F8", name = "nba2k23_var1",
         patch_offset = 5, patch_bytes = "90 90 90"},
        {pattern = "FF C8 83 F8 01 77", name = "nba2k23_var2",
         patch_offset = 2, patch_bytes = "90 90 90 90"},
        {pattern = "83 F8 01 77", name = "nba2k23_var3",
         patch_offset = 0, patch_bytes = "90 90 90 90"},

        -- Look for similar register operations that might be online checks
        {pattern = "8B ? 0C FF C8 83 F8", name = "nba2k23_generic1",
         patch_offset = 5, patch_bytes = "90 90 90"},
        {pattern = "89 ? ? 02 00 00 48", name = "nba2k23_generic2",
         patch_offset = 0, patch_bytes = "90 90 90 90 90 90 90"},
    }

    local patched_count = 0
    for i, pattern_info in ipairs(nba2k23_patterns) do
        local addr = Hook.markCodeScan(pattern_info.pattern)
        if addr and addr ~= 0 then
            print("[MyCareer] Found NBA2K23 pattern " .. pattern_info.name .. " at: " .. string.format("0x%X", addr))

            local patch_addr = addr + pattern_info.patch_offset
            local ptr = Pointer(patch_addr)
            if ptr then
                local success, err = pcall(function()
                    -- Read original bytes
                    local original_len = string.len(pattern_info.patch_bytes:gsub(" ", "")) / 2
                    local original = ptr:readByteArrayString(original_len)

                    -- Apply patch
                    ptr:writeByteArrayString(pattern_info.patch_bytes)

                    print("[MyCareer] Patched " .. pattern_info.name .. " at " .. string.format("0x%X", patch_addr))
                    print("[MyCareer] Original: " .. original .. " -> New: " .. pattern_info.patch_bytes)
                    patched_count = patched_count + 1
                end)

                if not success then
                    print("[MyCareer] Failed to patch " .. pattern_info.name .. ": " .. tostring(err))
                end
            end
        else
            print("[MyCareer] NBA2K23 pattern " .. pattern_info.name .. " not found")
        end
    end

    if patched_count > 0 then
        print("[MyCareer] Applied " .. patched_count .. " NBA2K23-specific patches")
        if Hook.showNotify then
            Hook.showNotify("Applied " .. patched_count .. " NBA2K23 patches - test MyCareer!", 1)
        end
        mycareer_offline.patches_applied = true
        return true
    end

    return false
end

-- Remove patches (restore original bytes)
function mycareer_offline.remove_patches()
    if mycareer_offline.patches_applied then
        print("[MyCareer] Removing MyCareer offline patches...")

        local patch_info = patches.online_check
        if patch_info.address and patch_info.original and patch_info.applied then
            print("[MyCareer] Restoring online check at: " .. string.format("0x%X", patch_info.address))
            
            local ptr = Pointer(patch_info.address)
            if ptr then
                local success, err = pcall(function()
                    ptr:writeByteArrayString(patch_info.original)
                end)

                if success then
                    print("[MyCareer] Successfully restored original online check")
                    patch_info.applied = false
                    mycareer_offline.patches_applied = false
                else
                    print("[MyCareer] Failed to restore original bytes: " .. tostring(err))
                end
            end
        end

        print("[MyCareer] Patch removal completed")
    end
end

-- Toggle the MyCareer offline functionality
function mycareer_offline.toggle_enabled()
    if mycareer_offline.enabled then
        -- Disable: Remove patches
        mycareer_offline.remove_patches()
        mycareer_offline.enabled = false
        print("[MyCareer] MyCareer offline mode DISABLED")
        if Hook and Hook.showNotify then
            Hook.showNotify("MyCareer offline mode DISABLED - online requirement restored", 2)
        end
    else
        -- Enable: Apply patches - start with NBA2K23-specific patterns
        mycareer_offline.enabled = true
        local success = mycareer_offline.try_nba2k23_patterns()

        if not success then
            print("[MyCareer] NBA2K23 patterns failed, trying NBA2K21 approach...")
            success = mycareer_offline.apply_online_patches()

            if not success then
                print("[MyCareer] NBA2K21 approach failed, trying alternative patterns...")
                success = mycareer_offline.try_alternative_patterns()

                if not success then
                    print("[MyCareer] Alternative patterns failed, trying string-based search...")
                    success = mycareer_offline.try_string_based_search()

                    if not success then
                        print("[MyCareer] All approaches failed, trying brute force method...")
                        success = mycareer_offline.try_brute_force_patches()
                    end
                end
            end
        end

        if success then
            print("[MyCareer] MyCareer offline mode ENABLED")
            if Hook and Hook.showNotify then
                Hook.showNotify("MyCareer offline mode ENABLED - try accessing MyCareer!", 1)
            end
        else
            print("[MyCareer] Failed to enable MyCareer offline mode")
            mycareer_offline.enabled = false
            if Hook and Hook.showNotify then
                Hook.showNotify("Failed to enable MyCareer offline mode - patterns not found", 3)
            end
        end
    end
end

-- Plugin lifecycle functions
function Init()
    print("[MyCareer] MyCareer Offline Mode Plugin initializing...")

    if not mycareer_offline.initialized then
        print("[MyCareer] Initializing MyCareer offline plugin...")
        
        -- Try to apply patches immediately - start with NBA2K23-specific patterns
        local success = mycareer_offline.try_nba2k23_patterns()

        if not success then
            print("[MyCareer] NBA2K23 patterns failed, trying NBA2K21 approach...")
            success = mycareer_offline.apply_online_patches()

            if not success then
                print("[MyCareer] NBA2K21 approach failed, trying alternative patterns...")
                success = mycareer_offline.try_alternative_patterns()

                if not success then
                    print("[MyCareer] Alternative patterns failed, trying string-based search...")
                    success = mycareer_offline.try_string_based_search()

                    if not success then
                        print("[MyCareer] String search failed, trying brute force...")
                        success = mycareer_offline.try_brute_force_patches()

                    if not success then
                        print("[MyCareer] All methods failed - no online check patterns found")
                        if Hook.showNotify then
                            Hook.showNotify("MyCareer offline plugin loaded but no effective patterns found. Use GUI to retry.", 2)
                        end
                    else
                        if Hook.showNotify then
                            Hook.showNotify("MyCareer offline plugin loaded! Applied brute force patches.", 1)
                        end
                    end
                else
                    if Hook.showNotify then
                        Hook.showNotify("MyCareer offline plugin loaded! Applied string-based patches.", 1)
                    end
                end
            else
                if Hook.showNotify then
                    Hook.showNotify("MyCareer offline plugin loaded! Applied alternative patches.", 1)
                end
            end
        else
            if Hook.showNotify then
                Hook.showNotify("MyCareer offline mode enabled! Online requirement removed.", 1)
            end
        end

        mycareer_offline.initialized = true
    end

    print("[MyCareer] Plugin initialized successfully!")
    print("[MyCareer] Try accessing MyCareer mode to test if online requirement is removed!")
    return true
end

function DeInit()
    print("[MyCareer] Plugin deinitializing...")
    mycareer_offline.remove_patches()
    mycareer_offline.initialized = false
    print("[MyCareer] Plugin deinitialized")
    return true
end

-- GUI functionality for the hook system
function OnDrawMenuItem()
    if not mycareer_offline.initialized then
        return
    end

    -- Check if ImGui is available
    if ImGui then
        -- Create a menu item for our plugin
        if ImGui.BeginMenu("MyCareer Offline") then

            -- Main toggle button
            local button_text = mycareer_offline.enabled and "Disable Offline Mode" or "Enable Offline Mode"
            if ImGui.MenuItem(button_text) then
                mycareer_offline.toggle_enabled()
            end

            -- Separator
            ImGui.Separator()

            -- Status display
            local status_text = mycareer_offline.enabled and "Status: ENABLED" or "Status: DISABLED"
            ImGui.Text(status_text)

            if mycareer_offline.patches_applied then
                ImGui.Text("Patches: Applied")
                ImGui.TextColored({0.0, 1.0, 0.0, 1.0}, "Online requirement removed!")
            else
                ImGui.Text("Patches: Not Applied")
                ImGui.TextColored({1.0, 0.5, 0.0, 1.0}, "Online requirement still active")
            end

            -- Separator
            ImGui.Separator()

            -- Manual controls
            if ImGui.MenuItem("Force Enable (Re-apply patches)") then
                mycareer_offline.enabled = true
                local success = mycareer_offline.try_nba2k23_patterns()
                if not success then
                    success = mycareer_offline.apply_online_patches()
                end
                if not success then
                    success = mycareer_offline.try_alternative_patterns()
                end
                if not success then
                    success = mycareer_offline.try_brute_force_patches()
                end
                if success and Hook.showNotify then
                    Hook.showNotify("MyCareer offline patches re-applied!", 1)
                end
            end

            if ImGui.MenuItem("Force Disable (Restore online requirement)") then
                mycareer_offline.remove_patches()
                mycareer_offline.enabled = false
                if Hook.showNotify then
                    Hook.showNotify("Online requirement restored!", 2)
                end
            end

            -- Separator
            ImGui.Separator()

            -- Info
            ImGui.Text("Plugin Version: V1.0")
            ImGui.Text("Based on NBA 2K21 cheat table")
            ImGui.Text("Removes online requirement for MyCareer")

            ImGui.EndMenu()
        end
    end
end

-- Console functions for easy access
function mc_toggle()
    if mycareer_offline and mycareer_offline.toggle_enabled then
        mycareer_offline.toggle_enabled()
    else
        print("[MyCareer] Plugin not loaded")
    end
end

function mc_status()
    if mycareer_offline then
        local status = mycareer_offline.enabled and "ENABLED" or "DISABLED"
        local patches = mycareer_offline.patches_applied and "Applied" or "Not Applied"
        print("[MyCareer] Status: " .. status .. ", Patches: " .. patches)
        if Hook and Hook.showNotify then
            Hook.showNotify("MyCareer Offline: " .. status .. " (Patches: " .. patches .. ")", 1)
        end
    else
        print("[MyCareer] Plugin not loaded")
    end
end

function mc_help()
    print("[MyCareer] Console Commands:")
    print("  mc_toggle() - Toggle MyCareer offline mode on/off")
    print("  mc_status() - Show current status")
    print("  mc_help()   - Show this help")
    print("")
    print("[MyCareer] GUI: Open hook menu (F8) and look for 'MyCareer Offline'")
end

print("[MyCareer] MyCareer Offline Plugin loaded - " .. (PluginInfo.name or "MyCareer Offline Mode") .. " " .. (PluginInfo.version or "V1.0"))
print("")
print("=== MYCAREER OFFLINE MODE PLUGIN READY ===")
print("Console Commands: mc_toggle(), mc_status(), mc_help()")
print("GUI: Press F8 to open hook menu, look for 'MyCareer Offline'")
print("===========================================")
print("")

-- Show initial status
mc_status()
