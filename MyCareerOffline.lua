-- MyCareerOffline.lua - NBA 2K23 MyCareer Offline Mode Plugin
-- Removes the online requirement for MyCareer mode based on NBA 2K21 cheat table

local lang = {}
lang.EN = {
    "MyCareer Offline Mode",
    "Removes the online requirement for MyCareer mode. Play MyCareer without internet connection!"
}
lang.ZH = {
    "MyCareer离线模式",
    "移除MyCareer模式的在线要求。无需网络连接即可游玩MyCareer！"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V1.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Memory patches based on NBA 2K21 working cheat table
local patches = {
    -- Online requirement check - based on NBA 2K21 pattern
    online_check = {
        -- NBA 2K21 pattern: 85 C9 0F 84 * * * * 3B CE 74 35 41 B8
        -- We'll search for similar patterns in NBA 2K23
        patterns = {
            "85 C9 0F 84 ? ? ? ? 3B CE 74 35 41 B8",  -- Exact NBA 2K21 pattern
            "85 C9 0F 84 ? ? ? ? 3B CE 74 35",        -- Shorter version
            "3B CE 74 35 41 B8",                      -- Just the patch area
            "85 C9 0F 84 ? ? ? ? 3B",                 -- Even shorter
            "3B CE 74 ?",                             -- Very short pattern
        },
        patch_offset = 8,  -- Offset to the "3B CE 74 35" part
        original_bytes = "3B CE 74 35",  -- Original bytes to replace
        patch_bytes = "90 90 90 90",     -- NOPs to disable the check
        address = nil,
        original = nil,
        applied = false
    }
}

-- Plugin state
local mycareer_offline = {
    enabled = true,
    initialized = false,
    patches_applied = false,
    gui_enabled = true,
    show_status = true
}

-- Try to find and patch the online requirement check
function mycareer_offline.apply_online_patches()
    print("[MyCareer] Searching for online requirement check patterns...")

    if not Hook or not Hook.markCodeScan then
        print("[MyCareer] Hook.markCodeScan not available")
        return false
    end

    local patch_info = patches.online_check

    -- Try each pattern until we find one
    for i, pattern in ipairs(patch_info.patterns) do
        print("[MyCareer] Trying pattern " .. i .. ": " .. pattern)
        local addr = Hook.markCodeScan(pattern)
        
        if addr and addr ~= 0 then
            print("[MyCareer] Found online check pattern at: " .. string.format("0x%X", addr))
            
            -- Calculate the actual patch address
            local patch_addr = addr + patch_info.patch_offset
            print("[MyCareer] Patch address: " .. string.format("0x%X", patch_addr))
            
            local ptr = Pointer(patch_addr)
            if ptr then
                -- Read original bytes first
                local success, original = pcall(function()
                    return ptr:readByteArrayString(4)  -- Read 4 bytes
                end)
                
                if success then
                    patch_info.original = original
                    patch_info.address = patch_addr
                    
                    print("[MyCareer] Original bytes: " .. original)
                    print("[MyCareer] Applying patch: " .. patch_info.patch_bytes)
                    
                    -- Apply the patch
                    local patch_success, err = pcall(function()
                        ptr:writeByteArrayString(patch_info.patch_bytes)
                    end)
                    
                    if patch_success then
                        print("[MyCareer] Successfully patched online requirement check!")
                        patch_info.applied = true
                        mycareer_offline.patches_applied = true
                        
                        if Hook.showNotify then
                            Hook.showNotify("MyCareer offline mode enabled! Online requirement removed.", 1)
                        end
                        return true
                    else
                        print("[MyCareer] Failed to apply patch: " .. tostring(err))
                    end
                else
                    print("[MyCareer] Failed to read original bytes")
                end
            else
                print("[MyCareer] Failed to create pointer for patch address")
            end
        else
            print("[MyCareer] Pattern " .. i .. " not found")
        end
    end

    print("[MyCareer] No suitable online check patterns found")
    return false
end

-- Try alternative patterns that might exist in NBA 2K23
function mycareer_offline.try_alternative_patterns()
    print("[MyCareer] Trying alternative online check patterns...")

    if not Hook or not Hook.markCodeScan then
        return false
    end

    -- Alternative patterns that might indicate online checks
    local alt_patterns = {
        -- Network/connection related patterns
        "E8 ? ? ? ? 85 C0 74 ? 48 8B",  -- call + test eax + je (common online check)
        "E8 ? ? ? ? 85 C0 75 ?",        -- call + test eax + jne
        "85 C0 0F 84 ? ? ? ?",          -- test eax + je (long jump)
        "85 C0 74 ?",                   -- test eax + je (short jump)
        "83 F8 00 74 ?",                -- cmp eax, 0 + je
        "83 F8 01 74 ?",                -- cmp eax, 1 + je
    }

    local found_count = 0
    for i, pattern in ipairs(alt_patterns) do
        local addr = Hook.markCodeScan(pattern)
        if addr and addr ~= 0 then
            print("[MyCareer] Found alternative pattern " .. i .. " at: " .. string.format("0x%X", addr))
            found_count = found_count + 1
            
            -- For now, just log the findings
            -- We could try to patch these, but it's riskier without knowing the exact context
        end
    end

    if found_count > 0 then
        print("[MyCareer] Found " .. found_count .. " potential online check patterns")
        if Hook.showNotify then
            Hook.showNotify("Found " .. found_count .. " potential online checks - manual analysis needed", 2)
        end
        return true
    end

    return false
end

-- Remove patches (restore original bytes)
function mycareer_offline.remove_patches()
    if mycareer_offline.patches_applied then
        print("[MyCareer] Removing MyCareer offline patches...")

        local patch_info = patches.online_check
        if patch_info.address and patch_info.original and patch_info.applied then
            print("[MyCareer] Restoring online check at: " .. string.format("0x%X", patch_info.address))
            
            local ptr = Pointer(patch_info.address)
            if ptr then
                local success, err = pcall(function()
                    ptr:writeByteArrayString(patch_info.original)
                end)

                if success then
                    print("[MyCareer] Successfully restored original online check")
                    patch_info.applied = false
                    mycareer_offline.patches_applied = false
                else
                    print("[MyCareer] Failed to restore original bytes: " .. tostring(err))
                end
            end
        end

        print("[MyCareer] Patch removal completed")
    end
end

-- Toggle the MyCareer offline functionality
function mycareer_offline.toggle_enabled()
    if mycareer_offline.enabled then
        -- Disable: Remove patches
        mycareer_offline.remove_patches()
        mycareer_offline.enabled = false
        print("[MyCareer] MyCareer offline mode DISABLED")
        if Hook and Hook.showNotify then
            Hook.showNotify("MyCareer offline mode DISABLED - online requirement restored", 2)
        end
    else
        -- Enable: Apply patches
        mycareer_offline.enabled = true
        local success = mycareer_offline.apply_online_patches()
        
        if not success then
            print("[MyCareer] Primary approach failed, trying alternative patterns...")
            success = mycareer_offline.try_alternative_patterns()
        end

        if success then
            print("[MyCareer] MyCareer offline mode ENABLED")
            if Hook and Hook.showNotify then
                Hook.showNotify("MyCareer offline mode ENABLED - try accessing MyCareer!", 1)
            end
        else
            print("[MyCareer] Failed to enable MyCareer offline mode")
            mycareer_offline.enabled = false
            if Hook and Hook.showNotify then
                Hook.showNotify("Failed to enable MyCareer offline mode - patterns not found", 3)
            end
        end
    end
end

-- Plugin lifecycle functions
function Init()
    print("[MyCareer] MyCareer Offline Mode Plugin initializing...")

    if not mycareer_offline.initialized then
        print("[MyCareer] Initializing MyCareer offline plugin...")
        
        -- Try to apply patches immediately
        local success = mycareer_offline.apply_online_patches()
        
        if not success then
            print("[MyCareer] Primary approach failed, trying alternative detection...")
            success = mycareer_offline.try_alternative_patterns()
            
            if not success then
                print("[MyCareer] No online check patterns found")
                if Hook.showNotify then
                    Hook.showNotify("MyCareer offline plugin loaded but no patterns found. Use GUI to retry.", 2)
                end
            else
                if Hook.showNotify then
                    Hook.showNotify("MyCareer offline plugin loaded! Found potential patterns.", 1)
                end
            end
        else
            if Hook.showNotify then
                Hook.showNotify("MyCareer offline mode enabled! Online requirement removed.", 1)
            end
        end

        mycareer_offline.initialized = true
    end

    print("[MyCareer] Plugin initialized successfully!")
    print("[MyCareer] Try accessing MyCareer mode to test if online requirement is removed!")
    return true
end

function DeInit()
    print("[MyCareer] Plugin deinitializing...")
    mycareer_offline.remove_patches()
    mycareer_offline.initialized = false
    print("[MyCareer] Plugin deinitialized")
    return true
end

-- GUI functionality for the hook system
function OnDrawMenuItem()
    if not mycareer_offline.initialized then
        return
    end

    -- Check if ImGui is available
    if ImGui then
        -- Create a menu item for our plugin
        if ImGui.BeginMenu("MyCareer Offline") then

            -- Main toggle button
            local button_text = mycareer_offline.enabled and "Disable Offline Mode" or "Enable Offline Mode"
            if ImGui.MenuItem(button_text) then
                mycareer_offline.toggle_enabled()
            end

            -- Separator
            ImGui.Separator()

            -- Status display
            local status_text = mycareer_offline.enabled and "Status: ENABLED" or "Status: DISABLED"
            ImGui.Text(status_text)

            if mycareer_offline.patches_applied then
                ImGui.Text("Patches: Applied")
                ImGui.TextColored({0.0, 1.0, 0.0, 1.0}, "Online requirement removed!")
            else
                ImGui.Text("Patches: Not Applied")
                ImGui.TextColored({1.0, 0.5, 0.0, 1.0}, "Online requirement still active")
            end

            -- Separator
            ImGui.Separator()

            -- Manual controls
            if ImGui.MenuItem("Force Enable (Re-apply patches)") then
                mycareer_offline.enabled = true
                local success = mycareer_offline.apply_online_patches()
                if success and Hook.showNotify then
                    Hook.showNotify("MyCareer offline patches re-applied!", 1)
                end
            end

            if ImGui.MenuItem("Force Disable (Restore online requirement)") then
                mycareer_offline.remove_patches()
                mycareer_offline.enabled = false
                if Hook.showNotify then
                    Hook.showNotify("Online requirement restored!", 2)
                end
            end

            -- Separator
            ImGui.Separator()

            -- Info
            ImGui.Text("Plugin Version: V1.0")
            ImGui.Text("Based on NBA 2K21 cheat table")
            ImGui.Text("Removes online requirement for MyCareer")

            ImGui.EndMenu()
        end
    end
end

-- Console functions for easy access
function mc_toggle()
    if mycareer_offline and mycareer_offline.toggle_enabled then
        mycareer_offline.toggle_enabled()
    else
        print("[MyCareer] Plugin not loaded")
    end
end

function mc_status()
    if mycareer_offline then
        local status = mycareer_offline.enabled and "ENABLED" or "DISABLED"
        local patches = mycareer_offline.patches_applied and "Applied" or "Not Applied"
        print("[MyCareer] Status: " .. status .. ", Patches: " .. patches)
        if Hook and Hook.showNotify then
            Hook.showNotify("MyCareer Offline: " .. status .. " (Patches: " .. patches .. ")", 1)
        end
    else
        print("[MyCareer] Plugin not loaded")
    end
end

function mc_help()
    print("[MyCareer] Console Commands:")
    print("  mc_toggle() - Toggle MyCareer offline mode on/off")
    print("  mc_status() - Show current status")
    print("  mc_help()   - Show this help")
    print("")
    print("[MyCareer] GUI: Open hook menu (F8) and look for 'MyCareer Offline'")
end

print("[MyCareer] MyCareer Offline Plugin loaded - " .. (PluginInfo.name or "MyCareer Offline Mode") .. " " .. (PluginInfo.version or "V1.0"))
print("")
print("=== MYCAREER OFFLINE MODE PLUGIN READY ===")
print("Console Commands: mc_toggle(), mc_status(), mc_help()")
print("GUI: Press F8 to open hook menu, look for 'MyCareer Offline'")
print("===========================================")
print("")

-- Show initial status
mc_status()
