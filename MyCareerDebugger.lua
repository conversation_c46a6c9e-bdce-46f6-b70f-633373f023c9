-- MyCareerDebugger.lua - NBA 2K23 MyCareer Debug and Analysis Tool
-- Helps identify online requirement patterns and test patches

local lang = {}
lang.EN = {
    "MyCareer Debugger",
    "Debug tool to analyze and identify MyCareer online requirement patterns in NBA 2K23"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V1.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Debug state
local debugger = {
    initialized = false,
    scanning = false,
    found_patterns = {},
    test_patches = {},
    log_buffer = {}
}

-- Add to log buffer with timestamp
function debugger.log(message)
    local timestamp = os.date("%H:%M:%S")
    local log_entry = "[" .. timestamp .. "] " .. message
    table.insert(debugger.log_buffer, log_entry)
    print(log_entry)
    
    -- Keep only last 100 entries
    if #debugger.log_buffer > 100 then
        table.remove(debugger.log_buffer, 1)
    end
end

-- Comprehensive pattern search based on NBA 2K21 knowledge
function debugger.scan_all_patterns()
    debugger.log("=== Starting comprehensive pattern scan ===")
    debugger.scanning = true
    debugger.found_patterns = {}
    
    if not Hook or not Hook.markCodeScan then
        debugger.log("ERROR: Hook.markCodeScan not available")
        return false
    end
    
    -- NBA 2K21 exact patterns (what we know works)
    local nba2k21_patterns = {
        {pattern = "85 C9 0F 84 ? ? ? ? 3B CE 74 35 41 B8", name = "NBA2K21_exact", description = "Exact NBA 2K21 online check"},
        {pattern = "85 C9 0F 84 ? ? ? ? 3B CE 74 35", name = "NBA2K21_short", description = "NBA 2K21 without end"},
        {pattern = "3B CE 74 35 41 B8", name = "NBA2K21_patch_area", description = "Just the patch area"},
        {pattern = "3B CE 74 35", name = "NBA2K21_core", description = "Core patch bytes"},
    }
    
    -- NBA 2K23 patterns from working cheat table
    local nba2k23_patterns = {
        {pattern = "8B 41 0C FF C8 83 F8 01 77", name = "NBA2K23_inject1", description = "From working cheat table"},
        {pattern = "89 B0 3C 02 00 00 48", name = "NBA2K23_inject2", description = "From working cheat table"},
        {pattern = "8B 41 0C FF C8 83 F8", name = "NBA2K23_var1", description = "Variation 1"},
        {pattern = "FF C8 83 F8 01 77", name = "NBA2K23_var2", description = "Variation 2"},
    }
    
    -- Generic online check patterns
    local generic_patterns = {
        {pattern = "E8 ? ? ? ? 85 C0 74 ?", name = "call_test_je", description = "Call + test + jump if zero"},
        {pattern = "E8 ? ? ? ? 85 C0 75 ?", name = "call_test_jne", description = "Call + test + jump if not zero"},
        {pattern = "85 C0 0F 84 ? ? ? ?", name = "test_je_long", description = "Test + long conditional jump"},
        {pattern = "85 C0 74 ?", name = "test_je_short", description = "Test + short conditional jump"},
        {pattern = "83 F8 00 74 ?", name = "cmp_0_je", description = "Compare with 0 + jump if equal"},
        {pattern = "83 F8 01 74 ?", name = "cmp_1_je", description = "Compare with 1 + jump if equal"},
    }
    
    -- Scan all pattern categories
    local all_categories = {
        {patterns = nba2k21_patterns, category = "NBA2K21"},
        {patterns = nba2k23_patterns, category = "NBA2K23"},
        {patterns = generic_patterns, category = "Generic"}
    }
    
    local total_found = 0
    for _, category in ipairs(all_categories) do
        debugger.log("--- Scanning " .. category.category .. " patterns ---")
        
        for _, pattern_info in ipairs(category.patterns) do
            debugger.log("Searching for " .. pattern_info.name .. ": " .. pattern_info.pattern)
            
            local addr = Hook.markCodeScan(pattern_info.pattern)
            if addr and addr ~= 0 then
                debugger.log("FOUND " .. pattern_info.name .. " at: " .. string.format("0x%X", addr))
                debugger.log("Description: " .. pattern_info.description)
                
                -- Store the found pattern
                table.insert(debugger.found_patterns, {
                    name = pattern_info.name,
                    address = addr,
                    pattern = pattern_info.pattern,
                    description = pattern_info.description,
                    category = category.category
                })
                
                total_found = total_found + 1
                
                -- Try to read surrounding bytes for context
                local ptr = Pointer(addr)
                if ptr then
                    local success, context = pcall(function()
                        return ptr:readByteArrayString(16)  -- Read 16 bytes for context
                    end)
                    if success then
                        debugger.log("Context bytes: " .. context)
                    end
                end
            else
                debugger.log("Not found: " .. pattern_info.name)
            end
        end
    end
    
    debugger.log("=== Pattern scan complete ===")
    debugger.log("Total patterns found: " .. total_found)
    debugger.scanning = false
    
    if Hook.showNotify then
        Hook.showNotify("Pattern scan complete! Found " .. total_found .. " patterns. Check console for details.", 1)
    end
    
    return total_found > 0
end

-- Test patch a specific pattern
function debugger.test_patch(pattern_index, patch_bytes)
    if not debugger.found_patterns[pattern_index] then
        debugger.log("ERROR: Invalid pattern index " .. pattern_index)
        return false
    end
    
    local pattern = debugger.found_patterns[pattern_index]
    debugger.log("Testing patch on " .. pattern.name .. " at " .. string.format("0x%X", pattern.address))
    
    local ptr = Pointer(pattern.address)
    if not ptr then
        debugger.log("ERROR: Could not create pointer for address")
        return false
    end
    
    -- Read original bytes
    local patch_len = string.len(patch_bytes:gsub(" ", "")) / 2
    local success, original = pcall(function()
        return ptr:readByteArrayString(patch_len)
    end)
    
    if not success then
        debugger.log("ERROR: Could not read original bytes")
        return false
    end
    
    debugger.log("Original bytes: " .. original)
    debugger.log("Patch bytes: " .. patch_bytes)
    
    -- Apply patch
    local patch_success, err = pcall(function()
        ptr:writeByteArrayString(patch_bytes)
    end)
    
    if patch_success then
        debugger.log("SUCCESS: Patch applied!")
        
        -- Store patch info for potential reversal
        table.insert(debugger.test_patches, {
            pattern = pattern,
            original = original,
            patch = patch_bytes,
            address = pattern.address
        })
        
        if Hook.showNotify then
            Hook.showNotify("Test patch applied to " .. pattern.name .. "! Try MyCareer now.", 1)
        end
        return true
    else
        debugger.log("ERROR: Failed to apply patch: " .. tostring(err))
        return false
    end
end

-- Revert all test patches
function debugger.revert_all_patches()
    debugger.log("Reverting all test patches...")
    
    for i, patch_info in ipairs(debugger.test_patches) do
        local ptr = Pointer(patch_info.address)
        if ptr then
            local success, err = pcall(function()
                ptr:writeByteArrayString(patch_info.original)
            end)
            
            if success then
                debugger.log("Reverted patch " .. i .. " at " .. string.format("0x%X", patch_info.address))
            else
                debugger.log("ERROR: Failed to revert patch " .. i .. ": " .. tostring(err))
            end
        end
    end
    
    debugger.test_patches = {}
    debugger.log("All patches reverted")
    
    if Hook.showNotify then
        Hook.showNotify("All test patches reverted", 2)
    end
end

-- Auto-test common patches on found patterns
function debugger.auto_test_patches()
    debugger.log("=== Starting auto-test of common patches ===")
    
    if #debugger.found_patterns == 0 then
        debugger.log("No patterns found to test. Run scan first.")
        return false
    end
    
    -- Common patch strategies
    local patch_strategies = {
        {name = "NOP_4", bytes = "90 90 90 90", description = "4 NOPs"},
        {name = "NOP_2", bytes = "90 90", description = "2 NOPs"},
        {name = "JMP_short", bytes = "EB", description = "Change JE to JMP"},
        {name = "XOR_EAX", bytes = "31 C0", description = "XOR EAX,EAX (set to 0)"},
    }
    
    local patches_applied = 0
    for i, pattern in ipairs(debugger.found_patterns) do
        debugger.log("Testing patches on pattern " .. i .. ": " .. pattern.name)
        
        -- Try different patch strategies based on pattern type
        if string.find(pattern.name, "NBA2K21") then
            -- For NBA 2K21 patterns, try the known working patch
            if debugger.test_patch(i, "90 90 90 90") then
                patches_applied = patches_applied + 1
                break  -- Stop after first successful patch
            end
        elseif string.find(pattern.name, "test_je") or string.find(pattern.name, "cmp") then
            -- For conditional jumps, try NOPs
            if debugger.test_patch(i, "90 90") then
                patches_applied = patches_applied + 1
            end
        end
    end
    
    debugger.log("=== Auto-test complete ===")
    debugger.log("Patches applied: " .. patches_applied)
    
    if patches_applied > 0 and Hook.showNotify then
        Hook.showNotify("Applied " .. patches_applied .. " test patches! Try MyCareer now.", 1)
    end
    
    return patches_applied > 0
end

-- Plugin lifecycle
function Init()
    debugger.log("MyCareer Debugger initializing...")
    debugger.initialized = true
    debugger.log("Use GUI menu to start pattern scanning and testing")
    return true
end

function DeInit()
    debugger.log("MyCareer Debugger deinitializing...")
    debugger.revert_all_patches()
    debugger.initialized = false
    return true
end

-- GUI
function OnDrawMenuItem()
    if not debugger.initialized then
        return
    end
    
    if ImGui then
        if ImGui.BeginMenu("MyCareer Debugger") then
            
            -- Scanning section
            if ImGui.MenuItem("Scan All Patterns") then
                debugger.scan_all_patterns()
            end
            
            ImGui.Separator()
            
            -- Pattern results
            if #debugger.found_patterns > 0 then
                ImGui.Text("Found Patterns: " .. #debugger.found_patterns)
                
                for i, pattern in ipairs(debugger.found_patterns) do
                    if ImGui.MenuItem(i .. ". " .. pattern.name .. " (" .. pattern.category .. ")") then
                        debugger.log("Selected pattern: " .. pattern.name .. " at " .. string.format("0x%X", pattern.address))
                    end
                end
                
                ImGui.Separator()
                
                -- Testing section
                if ImGui.MenuItem("Auto-Test Common Patches") then
                    debugger.auto_test_patches()
                end
                
                if ImGui.MenuItem("Revert All Patches") then
                    debugger.revert_all_patches()
                end
            else
                ImGui.Text("No patterns found - run scan first")
            end
            
            ImGui.Separator()
            
            -- Status
            ImGui.Text("Test patches applied: " .. #debugger.test_patches)
            if debugger.scanning then
                ImGui.Text("Status: Scanning...")
            else
                ImGui.Text("Status: Ready")
            end
            
            ImGui.EndMenu()
        end
    end
end

-- Console commands
function debug_scan()
    debugger.scan_all_patterns()
end

function debug_test()
    debugger.auto_test_patches()
end

function debug_revert()
    debugger.revert_all_patches()
end

function debug_status()
    debugger.log("=== MyCareer Debugger Status ===")
    debugger.log("Patterns found: " .. #debugger.found_patterns)
    debugger.log("Test patches applied: " .. #debugger.test_patches)
    debugger.log("Last " .. math.min(10, #debugger.log_buffer) .. " log entries:")
    
    local start_idx = math.max(1, #debugger.log_buffer - 9)
    for i = start_idx, #debugger.log_buffer do
        print(debugger.log_buffer[i])
    end
end

debugger.log("MyCareer Debugger loaded!")
debugger.log("Console commands: debug_scan(), debug_test(), debug_revert(), debug_status()")
debugger.log("GUI: Press F8 -> MyCareer Debugger")
