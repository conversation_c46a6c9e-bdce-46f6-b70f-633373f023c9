-- MyCareerMultiPatch.lua - NBA 2K23 MyCareer Multi-Pattern Patch
-- Tries all patterns found by debugger with different patch strategies

local lang = {}
lang.EN = {
    "MyCareer Multi-Patch",
    "Tries multiple patterns and patch strategies to remove MyCareer online requirement"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V1.2",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- All patterns found by debugger with different patch strategies
local patch_targets = {
    {
        name = "NBA2K21_core",
        address = 0x7FF6734B9607,
        pattern = "3B CE 74 35",
        strategies = {
            {name = "NOP_all", bytes = "90 90 90 90", desc = "NOP all 4 bytes"},
            {name = "JMP_unconditional", bytes = "3B CE EB 35", desc = "Change JE to JMP"},
            {name = "NOP_jump", bytes = "3B CE 90 90", desc = "NOP just the jump"},
        }
    },
    {
        name = "NBA2K23_inject1", 
        address = 0x7FF672580629,
        pattern = "8B 41 0C FF C8 83 F8 01 77",
        strategies = {
            {name = "NOP_cmp_jump", bytes = "8B 41 0C FF C8 90 90 90 90", desc = "NOP the compare and jump"},
            {name = "NOP_all", bytes = "90 90 90 90 90 90 90 90 90", desc = "NOP everything"},
            {name = "Force_pass", bytes = "8B 41 0C FF C8 83 F8 00 76", desc = "Change to always pass"},
        }
    },
    {
        name = "NBA2K23_var1",
        address = 0x7FF6725805C9,
        pattern = "8B 41 0C FF C8 83 F8",
        strategies = {
            {name = "NOP_end", bytes = "8B 41 0C FF C8 90 90", desc = "NOP the compare"},
            {name = "Force_zero", bytes = "8B 41 0C 31 C0 90 90", desc = "XOR EAX after load"},
        }
    },
    {
        name = "NBA2K23_var2",
        address = 0x7FF6724A4C89,
        pattern = "FF C8 83 F8 01 77",
        strategies = {
            {name = "NOP_all", bytes = "90 90 90 90 90 90", desc = "NOP all"},
            {name = "Force_pass", bytes = "FF C8 83 F8 00 76", desc = "Change condition"},
        }
    }
}

-- Plugin state
local multipatch = {
    initialized = false,
    applied_patches = {},
    successful_patches = 0
}

-- Log function
function multipatch.log(message)
    local timestamp = os.date("%H:%M:%S")
    print("[MultiPatch:" .. timestamp .. "] " .. message)
end

-- Verify and patch a single target
function multipatch.try_patch_target(target)
    multipatch.log("=== Trying target: " .. target.name .. " ===")
    multipatch.log("Address: " .. string.format("0x%X", target.address))
    
    local ptr = Pointer(target.address)
    if not ptr then
        multipatch.log("ERROR: Could not create pointer")
        return false
    end
    
    -- Read current bytes
    local pattern_len = string.len(target.pattern:gsub(" ", "")) / 2
    local success, current_bytes = pcall(function()
        return ptr:readByteArrayString(pattern_len)
    end)
    
    if not success then
        multipatch.log("ERROR: Could not read bytes")
        return false
    end
    
    multipatch.log("Current bytes: " .. current_bytes)
    multipatch.log("Expected: " .. target.pattern)
    
    -- Try each strategy for this target
    for i, strategy in ipairs(target.strategies) do
        multipatch.log("--- Trying strategy " .. i .. ": " .. strategy.name .. " ---")
        multipatch.log("Description: " .. strategy.desc)
        multipatch.log("Patch bytes: " .. strategy.bytes)
        
        -- Apply the patch
        local patch_success, err = pcall(function()
            ptr:writeByteArrayString(strategy.bytes)
        end)
        
        if patch_success then
            multipatch.log("✓ SUCCESS: Applied " .. strategy.name .. " to " .. target.name)
            
            -- Store patch info for potential reversal
            table.insert(multipatch.applied_patches, {
                target = target,
                strategy = strategy,
                original_bytes = current_bytes,
                address = target.address
            })
            
            multipatch.successful_patches = multipatch.successful_patches + 1
            
            if Hook.showNotify then
                Hook.showNotify("Applied patch " .. multipatch.successful_patches .. ": " .. target.name .. " (" .. strategy.name .. ")", 1)
            end
            
            return true
        else
            multipatch.log("✗ FAILED: " .. strategy.name .. " - " .. tostring(err))
            
            -- Try to restore original bytes before next attempt
            pcall(function()
                ptr:writeByteArrayString(current_bytes)
            end)
        end
    end
    
    multipatch.log("All strategies failed for " .. target.name)
    return false
end

-- Apply patches to all targets
function multipatch.apply_all_patches()
    multipatch.log("=== Starting multi-patch application ===")
    multipatch.applied_patches = {}
    multipatch.successful_patches = 0
    
    for i, target in ipairs(patch_targets) do
        multipatch.try_patch_target(target)
        
        -- Small delay between patches (removed setTimeout due to API issue)
    end
    
    multipatch.log("=== Multi-patch complete ===")
    multipatch.log("Successfully applied " .. multipatch.successful_patches .. " patches")
    
    if multipatch.successful_patches > 0 then
        multipatch.log("✓ Try accessing MyCareer now!")
        if Hook.showNotify then
            Hook.showNotify("Applied " .. multipatch.successful_patches .. " patches total! Test MyCareer now!", 1)
        end
        return true
    else
        multipatch.log("✗ No patches were successfully applied")
        if Hook.showNotify then
            Hook.showNotify("No patches could be applied - check console for details", 3)
        end
        return false
    end
end

-- Revert all applied patches
function multipatch.revert_all_patches()
    multipatch.log("=== Reverting all patches ===")
    
    for i, patch_info in ipairs(multipatch.applied_patches) do
        multipatch.log("Reverting patch " .. i .. ": " .. patch_info.target.name)
        
        local ptr = Pointer(patch_info.address)
        if ptr then
            local success, err = pcall(function()
                ptr:writeByteArrayString(patch_info.original_bytes)
            end)
            
            if success then
                multipatch.log("✓ Reverted " .. patch_info.target.name)
            else
                multipatch.log("✗ Failed to revert " .. patch_info.target.name .. ": " .. tostring(err))
            end
        end
    end
    
    multipatch.applied_patches = {}
    multipatch.successful_patches = 0
    multipatch.log("=== Revert complete ===")
    
    if Hook.showNotify then
        Hook.showNotify("All patches reverted - online requirement restored", 2)
    end
end

-- Try a more aggressive approach - patch all conditional jumps in the found areas
function multipatch.aggressive_patch()
    multipatch.log("=== Starting aggressive patching ===")
    multipatch.log("This will patch ALL conditional jumps in the found areas")
    
    local aggressive_patches = 0
    
    for _, target in ipairs(patch_targets) do
        multipatch.log("Aggressive patching around " .. target.name)
        
        -- Patch a wider area around each target
        for offset = -10, 10, 2 do  -- Check every 2 bytes in a 20-byte range
            local test_addr = target.address + offset
            local ptr = Pointer(test_addr)
            
            if ptr then
                local success, bytes = pcall(function()
                    return ptr:readByteArrayString(2)
                end)
                
                if success then
                    -- Look for conditional jump opcodes and NOP them
                    local first_byte = string.sub(bytes, 1, 2)
                    if first_byte == "74" or first_byte == "75" or first_byte == "77" or first_byte == "76" then
                        multipatch.log("Found conditional jump at offset " .. offset .. ": " .. bytes)
                        
                        local patch_success, err = pcall(function()
                            ptr:writeByteArrayString("90 90")  -- NOP NOP
                        end)
                        
                        if patch_success then
                            multipatch.log("✓ Patched conditional jump at " .. string.format("0x%X", test_addr))
                            aggressive_patches = aggressive_patches + 1
                        end
                    end
                end
            end
        end
    end
    
    multipatch.log("=== Aggressive patching complete ===")
    multipatch.log("Patched " .. aggressive_patches .. " conditional jumps")
    
    if aggressive_patches > 0 and Hook.showNotify then
        Hook.showNotify("Aggressive patch: Modified " .. aggressive_patches .. " jumps! Test MyCareer!", 1)
    end
    
    return aggressive_patches > 0
end

-- Plugin lifecycle
function Init()
    multipatch.log("MyCareer Multi-Patch initializing...")
    
    if not multipatch.initialized then
        multipatch.log("Found " .. #patch_targets .. " targets from debugger")
        
        -- Auto-apply patches
        multipatch.apply_all_patches()
        
        multipatch.initialized = true
    end
    
    multipatch.log("Plugin initialized - " .. multipatch.successful_patches .. " patches applied")
    return true
end

function DeInit()
    multipatch.log("Deinitializing...")
    multipatch.revert_all_patches()
    multipatch.initialized = false
    return true
end

-- GUI
function OnDrawMenuItem()
    if not multipatch.initialized then
        return
    end
    
    if ImGui then
        if ImGui.BeginMenu("MyCareer Multi-Patch") then
            
            ImGui.Text("Applied patches: " .. multipatch.successful_patches)
            ImGui.Text("Total targets: " .. #patch_targets)
            
            ImGui.Separator()
            
            if ImGui.MenuItem("Apply All Patches") then
                multipatch.apply_all_patches()
            end
            
            if ImGui.MenuItem("Revert All Patches") then
                multipatch.revert_all_patches()
            end
            
            ImGui.Separator()
            
            if ImGui.MenuItem("Aggressive Patch (Risky)") then
                multipatch.aggressive_patch()
            end
            
            ImGui.Separator()
            
            ImGui.Text("Version: V1.2")
            if multipatch.successful_patches > 0 then
                ImGui.TextColored({0.0, 1.0, 0.0, 1.0}, "Patches applied - test MyCareer!")
            else
                ImGui.TextColored({1.0, 0.5, 0.0, 1.0}, "No patches applied")
            end
            
            ImGui.EndMenu()
        end
    end
end

-- Console commands
function mcp_apply()
    multipatch.apply_all_patches()
end

function mcp_revert()
    multipatch.revert_all_patches()
end

function mcp_aggressive()
    multipatch.aggressive_patch()
end

function mcp_status()
    multipatch.log("Status: " .. multipatch.successful_patches .. " patches applied")
    for i, patch in ipairs(multipatch.applied_patches) do
        multipatch.log("  " .. i .. ". " .. patch.target.name .. " (" .. patch.strategy.name .. ")")
    end
end

multipatch.log("Multi-Patch Plugin loaded!")
multipatch.log("Console: mcp_apply(), mcp_revert(), mcp_aggressive(), mcp_status()")
multipatch.log("GUI: F8 -> MyCareer Multi-Patch")
